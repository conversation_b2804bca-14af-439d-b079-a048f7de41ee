<!-- NodeEditDialog.svelte -->
<script>
	import {
		<PERSON>alog,
		DialogContent,
		DialogDescription,
		DialogFooter,
		DialogHeader,
		DialogTitle
	} from '$lib/components/ui/dialog';
	import { Button } from '$lib/components/ui/button';
	import BaseSettingComponents from './settings/BaseSettingComponents.svelte';
	import ComplexSettingComponents from './settings/ComplexSettingComponents.svelte';
	import { settingConfigs } from './settings/settingsConfig.js';
	import { validateValue, autoAdjustSongTypesSelection, deepClone, getTotalSongs } from './settings/validationUtils.js';

	let {
		open = $bindable(false),
		nodeData = $bindable(null),
		onSave = () => {}
	} = $props();

	// Local state for editing
	let editedValue = $state(null);
	let isValid = $state(true);
	let validationMessage = $state('');

	// Get node color for slider styling
	const getNodeColor = () => {
		return nodeData?.color || '#6366f1'; // Default to indigo if no color
	};

	// Watch for nodeData changes to initialize editedValue
	$effect(() => {
		if (nodeData && open) {
			const settingId = nodeData.id?.replace('-setting', '');
			const config = settingConfigs[settingId];

			if (!config) {
				editedValue = null;
				return;
			}

			// Initialize editedValue based on the setting type and current value
			if (config.type === 'number-with-random') {
				if (typeof nodeData.currentValue === 'object' && nodeData.currentValue.random !== undefined) {
					editedValue = deepClone(nodeData.currentValue);
				} else {
					editedValue = { random: false, value: nodeData.currentValue, min: config.min, max: config.max };
				}
			} else if (config.type === 'select-with-random') {
				if (typeof nodeData.currentValue === 'object' && nodeData.currentValue.random !== undefined) {
					editedValue = deepClone(nodeData.currentValue);
				} else {
					const options = {};
					config.options.forEach(opt => options[opt.value] = false);
					editedValue = { random: false, value: nodeData.currentValue, options };
				}
			} else {
				editedValue = deepClone(nodeData.currentValue);
			}
		}
	});

	// Get configuration for current node
	const config = $derived(nodeData ? settingConfigs[nodeData.id?.replace('-setting', '')] : null);

	// This determines which CSS class and layout to use.
	const dialogSize = $derived(config?.size || 'medium');

	// Get total songs setting for validation context
	const getTotalSongsForNode = () => {
		return getTotalSongs(nodeData);
	};

	// Real-time validation and auto-adjustment for song types
	$effect(() => {
		if (editedValue && config?.type === 'complex-song-types-selection') {
			autoAdjustSongTypesSelection(editedValue, getTotalSongsForNode());
		}
	});

	// Update validation when editedValue changes
	$effect(() => {
		if (editedValue && config) {
			const validation = validateValue(editedValue, config);
			isValid = validation.valid;
			validationMessage = validation.message;
		}
	});

	function handleSave() {
		if (!isValid || !nodeData) return;
		
		onSave({
			nodeId: nodeData.id,
			newValue: editedValue
		});
		
		open = false;
	}

	function handleCancel() {
		open = false;
		editedValue = null;
	}
</script>

<Dialog {open} onOpenChange={(v) => open = v}>
	<!-- Apply a dynamic class based on the 'size' from config -->
	<DialogContent
		class="dialog-size-{dialogSize} {dialogSize === 'fullscreen' ? 'p-0 border-0' : ''}"
		showCloseButton={false}
		portalProps={dialogSize === 'fullscreen' ? { class: 'fullscreen-portal' } : undefined}
	>

		<!-- RENDER FULLSCREEN LAYOUT -->
		{#if dialogSize === 'fullscreen'}
			<div class="fullscreen-container">
				<!-- Header -->
				<div class="fullscreen-header">
					<DialogHeader class="text-center">
						<DialogTitle class="flex items-center justify-center mb-2 text-3xl text-gray-900">
							<span class="mr-4 text-4xl" style="color: {getNodeColor()}">{nodeData?.icon}</span>
							Edit {nodeData?.title}
						</DialogTitle>
						<DialogDescription class="text-lg text-gray-600">
							Configure the settings for this node
						</DialogDescription>
					</DialogHeader>
				</div>

				<!-- Scrollable Content -->
				<div class="fullscreen-content">
					{#if config && editedValue !== null}
						<div class="min-h-full p-8 space-y-8">
							{@render SettingsForm()}
						</div>
					{/if}
				</div>

				<!-- Footer -->
				<div class="fullscreen-footer">
					<DialogFooter class="flex justify-center gap-4">
						<Button variant="outline" onclick={handleCancel} class="px-8 py-3 text-lg">Cancel</Button>
						<Button onclick={handleSave} disabled={!isValid} class="px-8 py-3 text-lg" style="background-color: {getNodeColor()};">Save Changes</Button>
					</DialogFooter>
				</div>
			</div>

		<!-- RENDER STANDARD (small, medium, large) LAYOUT -->
		{:else}
			<DialogHeader class="p-6 pb-4">
				<DialogTitle class="flex items-center text-xl">
					<span class="mr-3 text-2xl" style="color: {getNodeColor()}">{nodeData?.icon}</span>
					Edit {nodeData?.title}
				</DialogTitle>
				<DialogDescription class="pt-1 text-base">
					Configure the settings for this node.
				</DialogDescription>
			</DialogHeader>

			<div class="p-6 pt-0">
				{#if config && editedValue !== null}
					{@render SettingsForm()}
				{/if}
			</div>

			<DialogFooter class="p-6 pt-4">
				<Button variant="outline" onclick={handleCancel}>Cancel</Button>
				<Button onclick={handleSave} disabled={!isValid} style="background-color: {getNodeColor()};">Save</Button>
			</DialogFooter>
		{/if}
	</DialogContent>
</Dialog>

<!-- This snippet contains all form fields. It's rendered in both layouts. -->
{#snippet SettingsForm()}
	<!-- Check if it's a complex setting type -->
	{#if config.type.startsWith('complex-')}
		<ComplexSettingComponents
			{config}
			bind:editedValue
			{getNodeColor}
			getTotalSongs={getTotalSongsForNode}
		/>
	{:else}
		<!-- Use base setting components for simple types -->
		<BaseSettingComponents
			{config}
			bind:editedValue
			bind:isValid
			bind:validationMessage
			{getNodeColor}
		/>
	{/if}
{/snippet}

<style>
	/* --- Standard Modal Sizes --- */
	/* We rely on shadcn/bits-ui defaults but override the max-width */
	:global(.dialog-size-small) {
		max-width: 25rem; /* ~400px */
	}
	:global(.dialog-size-medium) {
		max-width: 42rem; /* ~672px */
	}
	:global(.dialog-size-large) {
		max-width: 56rem; /* ~896px */
	}

	/* --- Fullscreen Modal Styles --- */
	/* These styles apply ONLY when the .dialog-size-fullscreen class is present */
	:global(.dialog-size-fullscreen) {
		width: calc(100vw - 4rem) !important;
		height: calc(100vh - 4rem) !important;
		max-width: none !important;
		max-height: none !important;
		margin: 0 !important;
		padding: 0 !important;
		border-radius: 12px !important;
		position: fixed !important;
		top: 2rem !important;
		left: 2rem !important;
		right: 2rem !important;
		bottom: 2rem !important;
		transform: none !important;
		translate: none !important;
		border: 1px solid #e5e7eb !important;
		box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
		animation: modalFadeIn 0.3s ease-out !important;
		background: white !important;
		inset: 2rem !important;
	}

	@keyframes modalFadeIn {
		from {
			opacity: 0;
			transform: scale(0.95);
		}
		to {
			opacity: 1;
			transform: scale(1);
		}
	}

	/* Override default dialog positioning for fullscreen */
	:global([data-slot="dialog-content"].dialog-size-fullscreen) {
		position: fixed !important;
		top: 2rem !important;
		left: 2rem !important;
		right: 2rem !important;
		bottom: 2rem !important;
		width: auto !important;
		height: auto !important;
		max-width: none !important;
		max-height: none !important;
		transform: none !important;
		translate: none !important;
		margin: 0 !important;
		inset: 2rem !important;
	}

	/* Fullscreen container layout */
	.fullscreen-container {
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;
		border-radius: 12px;
		overflow: hidden;
	}

	.fullscreen-header {
		flex-shrink: 0;
		padding: 2rem 2rem 1.5rem 2rem;
		border-bottom: 1px solid #e5e7eb;
		background: linear-gradient(to right, #f9fafb, #f3f4f6);
	}

	.fullscreen-content {
		flex: 1;
		overflow-y: auto;
		background: linear-gradient(to bottom right, #f9fafb, #f3f4f6);
	}

	.fullscreen-footer {
		flex-shrink: 0;
		padding: 1.5rem 2rem 2rem 2rem;
		border-top: 1px solid #e5e7eb;
		background: #f9fafb;
	}
</style>
